import os

def filter_log_file(input_path, output_path, filter_text):
    """
    Remove lines containing specific text from a file and save to new file.
    
    Args:
        input_path (str): Path to input file
        output_path (str): Path to output file
        filter_text (str): Text to filter out (lines containing this will be removed)
    """
    try:
        with open(input_path, 'r', encoding='utf-8') as input_file:
            lines = input_file.readlines()
        
        # Filter out lines containing the specified text
        filtered_lines = [line for line in lines if filter_text not in line]
        
        # Write filtered lines to output file
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.writelines(filtered_lines)
        
        # Print summary
        original_count = len(lines)
        filtered_count = len(filtered_lines)
        removed_count = original_count - filtered_count
        
        print(f"Processing complete!")
        print(f"Original lines: {original_count}")
        print(f"Lines removed: {removed_count}")
        print(f"Remaining lines: {filtered_count}")
        print(f"Output saved to: {output_path}")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_path}' not found.")
    except PermissionError:
        print(f"Error: Permission denied. Check file permissions.")
    except Exception as e:
        print(f"Error: {str(e)}")

# Usage
input_file = r"D:\diagnose\log.txt"
output_file = r"D:\diagnose\small.txt"
filter_string = "push.personal.liquidate.risk"

# Create output directory if it doesn't exist
os.makedirs(os.path.dirname(output_file), exist_ok=True)

# Run the filter
filter_log_file(input_file, output_file, filter_string)
